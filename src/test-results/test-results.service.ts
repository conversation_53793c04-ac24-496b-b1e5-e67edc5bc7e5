import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TestResult, TestResultStatus } from './test-result.entity';
import { Storage } from '@google-cloud/storage';

@Injectable()
export class TestResultsService {
  private storage: Storage;
  private bucketName: string;

  constructor(
    @InjectRepository(TestResult)
    private testResultsRepository: Repository<TestResult>,
  ) {
    // Initialize Google Cloud Storage
    this.storage = new Storage({
      projectId: process.env.GCP_PROJECT_ID,
      credentials: {
        client_email: process.env.GCP_CLIENT_EMAIL,
        private_key: process.env.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
    this.bucketName = process.env.GCP_BUCKET_NAME || 'agentq-test-logs';
  }

  /**
   * Create a new test result (simple, no project/testRun required)
   */
  async createSimpleTestResult(
    createDto: { id?: string; testRunId: string; testCaseId: string; status?: string; createdBy?: string }
  ): Promise<{ success: boolean; id: string }> {
    // Import the TestResultStatus enum
    const statusEnum = (this.testResultsRepository.metadata.columns.find(col => col.propertyName === 'status')?.enum || ['untested'])[0];
    const entity = this.testResultsRepository.create({
      testRunId: createDto.testRunId,
      testCaseId: createDto.testCaseId,
      status: (createDto.status as any) || statusEnum || 'untested',
      createdBy: createDto.createdBy || null,
    });
    await this.testResultsRepository.save(entity);
    return { success: true, id: entity.id };
  }

  /**
   * Patch videoUrl, screenshotUrl, and/or logsSecurityUrl for a test result by testResultId
   */
  async patchTestResult(
    testResultId: string,
    updateDto: { videoUrl?: string; screenshotUrl?: string; logsSecurityUrl?: string; createdBy?: string }
  ): Promise<{ success: boolean }> {
    const updateFields: any = {};
    if (updateDto.videoUrl !== undefined) updateFields.videoUrl = updateDto.videoUrl;
    if (updateDto.screenshotUrl !== undefined) updateFields.screenshotUrl = updateDto.screenshotUrl;
    if (updateDto.logsSecurityUrl !== undefined) updateFields.logsSecurityUrl = updateDto.logsSecurityUrl;
    if (updateDto.createdBy !== undefined) updateFields.createdBy = updateDto.createdBy;

    if (Object.keys(updateFields).length === 0) {
      throw new Error('No fields to update');
    }

    try {
      const result = await this.testResultsRepository.update(
        { id: testResultId },
        updateFields
      );

      if (result.affected && result.affected > 0) {
        return { success: true };
      } else {
        console.error(`patchTestResult: No test result found for id=${testResultId}`);
        throw new Error('Test result not found or not updated');
      }
    } catch (err) {
      console.error(`patchTestResult: Error updating testResultId=${testResultId}`, err);
      throw err;
    }
  }

  /**
   * Update test result with video URL
   */
  async updateTestResultVideoUrl(resultId: string, videoUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { videoUrl });
  }

  /**
   * Update test result with screenshot URL
   */
  async updateTestResultScreenshotUrl(resultId: string, screenshotUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { screenshotUrl });
  }

  /**
   * Update test result with logs URL
   */
  async updateTestResultLogsUrl(resultId: string, logsUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { logsUrl });
  }

  /**
   * Update test result with security logs URL (DAST)
   */
  async updateTestResultSecurityLogsUrl(resultId: string, logsSecurityUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { logsSecurityUrl });
  }

  /**
   * Update test result with vulnerability description (DAST)
   */
  async updateTestResultVulnerabilityDescription(resultId: string, vulnerabilityDescription: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { vulnerabilityDescription });
  }

  /**
   * Update additional DAST fields from ZAP report
   */
  async updateAdditionalDastFields(testResultId: string, zapReport: any): Promise<void> {
    try {
      const updateData: any = {};

      // Extract affected URLs from scan context
      if (zapReport.scanContext?.sites) {
        const affectedUrls = zapReport.scanContext.sites.slice(0, 20).join('\n'); // Limit to first 20 URLs
        updateData.affectedUrls = affectedUrls;
      }

      // For DAST tests, always set status to "New" for security review
      // regardless of whether vulnerabilities were found or not
      updateData.status = TestResultStatus.NEW;

      // Populate vulnerability count fields from ZAP report summary
      if (zapReport.summary) {
        console.log(`🔍 Debug: ZAP report summary found:`, zapReport.summary);
        const { highRisk, mediumRisk, lowRisk, informational } = zapReport.summary;

        // Set individual vulnerability counts
        updateData.highCount = highRisk || 0;
        updateData.mediumCount = mediumRisk || 0;
        updateData.lowCount = lowRisk || 0;
        updateData.informationalCount = informational || 0;
        updateData.falsePositiveCount = 0; // Default to 0, can be manually updated later

        console.log(`🔍 Debug: Vulnerability counts being set:`, {
          highCount: updateData.highCount,
          mediumCount: updateData.mediumCount,
          lowCount: updateData.lowCount,
          informationalCount: updateData.informationalCount,
          falsePositiveCount: updateData.falsePositiveCount
        });

        // Determine original severity based on highest risk level found
        if (highRisk > 0) {
          updateData.originalSeverity = 'High';
        } else if (mediumRisk > 0) {
          updateData.originalSeverity = 'Medium';
        } else if (lowRisk > 0) {
          updateData.originalSeverity = 'Low';
        } else if (informational > 0) {
          updateData.originalSeverity = 'Informational';
        } else {
          updateData.originalSeverity = 'None';
        }
      } else {
        console.warn(`⚠️ No summary found in ZAP report for test result ${testResultId}`);
        console.log(`🔍 Debug: Available ZAP report keys:`, Object.keys(zapReport || {}));

        // Set default values if no summary is available
        updateData.highCount = 0;
        updateData.mediumCount = 0;
        updateData.lowCount = 0;
        updateData.informationalCount = 0;
        updateData.falsePositiveCount = 0;
        updateData.originalSeverity = 'None';
      }

      // Update the test result with additional DAST data
      if (Object.keys(updateData).length > 0) {
        await this.testResultsRepository.update(testResultId, updateData);
        console.log(`✅ Additional DAST fields updated for test result: ${testResultId}`, updateData);
      }
    } catch (error) {
      console.error('Error updating additional DAST fields:', error);
    }
  }

  /**
   * Store ZAP security report for a test result
   */
  async storeSecurityReport(
    testResultId: string,
    zapReport: any,
    clientId?: string
  ): Promise<{ success: boolean; logsSecurityUrl: string }> {
    try {
      // Extract CLIENT_ID from zapReport if not provided
      const effectiveClientId = clientId || zapReport?.clientId || 'default';

      // Upload security logs to Google Cloud Storage with CLIENT_ID isolation
      const fileName = `test-results/logs-dast/${effectiveClientId}/${testResultId}/zap-report-${Date.now()}.json`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      // Convert zapReport to JSON string if it's an object
      const reportData = typeof zapReport === 'string' ? zapReport : JSON.stringify(zapReport, null, 2);

      await file.save(reportData, {
        metadata: {
          contentType: 'application/json',
          clientId: effectiveClientId, // Add CLIENT_ID to metadata for tracking
        },
      });

      const logsSecurityUrl = `gs://${this.bucketName}/${fileName}`;

      // Update the test result with security logs URL
      await this.updateTestResultSecurityLogsUrl(testResultId, logsSecurityUrl);

      // Debug: Log the ZAP report structure
      console.log(`🔍 Debug: ZAP report structure for ${testResultId}:`, {
        hasReport: !!zapReport,
        hasSummary: !!zapReport?.summary,
        hasScanContext: !!zapReport?.scanContext,
        summaryKeys: zapReport?.summary ? Object.keys(zapReport.summary) : [],
        scanContextKeys: zapReport?.scanContext ? Object.keys(zapReport.scanContext) : [],
        reportKeys: zapReport ? Object.keys(zapReport) : []
      });

      // Generate vulnerability description from DAST summary and scan context
      const vulnerabilityDescription = this.generateVulnerabilityDescription(zapReport);
      console.log(`🔍 Debug: Generated vulnerability description length: ${vulnerabilityDescription?.length || 0}`);

      if (vulnerabilityDescription) {
        await this.updateTestResultVulnerabilityDescription(testResultId, vulnerabilityDescription);
        console.log(`✅ Vulnerability description updated for test result: ${testResultId}`);
      } else {
        console.warn(`⚠️ No vulnerability description generated for test result: ${testResultId}`);
      }

      // Extract and update additional DAST fields
      await this.updateAdditionalDastFields(testResultId, zapReport);

      // Add a delay and then force the status to "New" again to ensure it's not overwritten
      // by the test completion logic
      setTimeout(async () => {
        try {
          console.log(`🔄 Final DAST status update: Setting test result ${testResultId} to "New" status`);
          await this.testResultsRepository.update(testResultId, { status: TestResultStatus.NEW });
          console.log(`✅ Final DAST status update completed for test result: ${testResultId}`);
        } catch (error) {
          console.error(`❌ Failed to apply final DAST status update for test result ${testResultId}:`, error);
        }
      }, 2000); // 2 second delay to ensure this runs after test completion

      console.log(`Security logs uploaded successfully to GCS for test result: ${testResultId} (CLIENT_ID: ${effectiveClientId})`);
      console.log(`🔒 CLIENT_ID isolation ensured in path: ${fileName}`);

      return { success: true, logsSecurityUrl };
    } catch (error) {
      console.error(`Failed to store security report for test result ${testResultId}:`, error);
      throw error;
    }
  }

  /**
   * Get HTML content from ZAP security report
   */
  async getSecurityReportHtml(logsSecurityUrl: string): Promise<string> {
    try {
      // Extract file path from GCS URL
      const filePath = logsSecurityUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      // Download the JSON report
      const [fileContent] = await file.download();
      const zapReport = JSON.parse(fileContent.toString());

      console.log(`🔍 Debug: ZAP report structure for HTML generation:`, {
        hasHtml: !!zapReport.html,
        hasSummary: !!zapReport.summary,
        hasVulnerabilities: !!zapReport.vulnerabilities,
        hasJson: !!zapReport.json,
        reportKeys: Object.keys(zapReport)
      });

      // Extract HTML content from the ZAP report
      if (zapReport.html && zapReport.html.trim().length > 0) {
        console.log(`✅ Using embedded HTML from ZAP report`);
        return this.customizeZapHtmlReport(zapReport.html);
      } else {
        // If no HTML content, generate a comprehensive HTML report from the JSON data
        console.log(`🔧 Generating HTML report from ZAP JSON data`);
        return this.generateHtmlFromZapReport(zapReport);
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error(`Failed to retrieve security report HTML: ${error.message}`);
      } else {
        throw new Error(`Failed to retrieve security report HTML: Unknown error`);
      }
    }
  }

  /**
   * Customize ZAP HTML report to replace ZAP branding with AgentQ branding
   */
  private customizeZapHtmlReport(originalHtml: string): string {
    let customizedHtml = originalHtml;

    // AGGRESSIVE IMAGE REMOVAL - Remove ALL img tags to eliminate ZAP logos
    customizedHtml = customizedHtml.replace(
      /<img[^>]*>/gi,
      ''
    );

    // Remove any remaining image-related elements
    customizedHtml = customizedHtml.replace(
      /<picture[^>]*>.*?<\/picture>/gis,
      ''
    );

    // Remove SVG elements that might contain ZAP logos
    customizedHtml = customizedHtml.replace(
      /<svg[^>]*>.*?<\/svg>/gis,
      ''
    );

    // Remove any div or span that might contain logo classes
    customizedHtml = customizedHtml.replace(
      /<div[^>]*class[^>]*logo[^>]*>.*?<\/div>/gis,
      ''
    );

    customizedHtml = customizedHtml.replace(
      /<span[^>]*class[^>]*logo[^>]*>.*?<\/span>/gis,
      ''
    );

    // COMPREHENSIVE TEXT REPLACEMENT

    // Remove "Sites:" line that shows all URLs
    customizedHtml = customizedHtml.replace(
      /Sites:\s*https?:\/\/[^\n\r]*[\n\r]*/gi,
      ''
    );

    // Remove entire lines/sections that contain ZAP branding
    customizedHtml = customizedHtml.replace(
      /.*ZAP\s+by\s+Checkmarx.*\n?/gi,
      ''
    );

    customizedHtml = customizedHtml.replace(
      /.*ZAP by Checkmarx.*\n?/gi,
      ''
    );

    // Remove "AgentQ Security Scanner" text
    customizedHtml = customizedHtml.replace(
      /AgentQ\s+Security\s+Scanner/gi,
      ''
    );

    // Remove any HTML elements containing ZAP branding
    customizedHtml = customizedHtml.replace(
      /<[^>]*>.*?ZAP\s+by\s+Checkmarx.*?<\/[^>]*>/gis,
      ''
    );

    // Replace ZAP title and headers (multiple variations)
    customizedHtml = customizedHtml.replace(
      /ZAP Scanning Report/gi,
      ''
    );

    // Replace ZAP branding text (multiple variations)
    customizedHtml = customizedHtml.replace(
      /ZAP by Checkmarx/gi,
      ''
    );

    customizedHtml = customizedHtml.replace(
      /ZAP\s+by\s+Checkmarx/gi,
      ''
    );

    // Replace any remaining "ZAP" references in headers
    customizedHtml = customizedHtml.replace(
      /<h[1-6][^>]*>.*?ZAP.*?<\/h[1-6]>/gi,
      (match) => match.replace(/ZAP/gi, 'AgentQ')
    );

    // Replace ZAP version references
    customizedHtml = customizedHtml.replace(
      /ZAP Version: [0-9.]+/gi,
      ''
    );

    // Remove or replace any remaining ZAP references
    customizedHtml = customizedHtml.replace(
      /Generated by ZAP/gi,
      'Generated by AgentQ'
    );

    // Replace any standalone "ZAP" text that might be in the content
    customizedHtml = customizedHtml.replace(
      /\bZAP\b(?!\s*<)/g,
      'AgentQ'
    );

    // Remove Checkmarx references completely
    customizedHtml = customizedHtml.replace(
      /Checkmarx/gi,
      ''
    );

    // Remove any remaining "by" text that might be left hanging
    customizedHtml = customizedHtml.replace(
      /\s+by\s*$/gm,
      ''
    );

    // remove "AgentQ by"
    customizedHtml = customizedHtml.replace(
      /AgentQ\s+by\s+/gi,
      ''
    );

    // Add simple AgentQ header at the beginning
    const agentqHeader = `
      <h1 style="color: #2d3748; font-size: 2.5rem; font-weight: 700; margin: 20px 0 30px 0; text-align: center;">AgentQ Security Report</h1>
    `;

    // Insert AgentQ header right after body tag
    if (customizedHtml.includes('<body')) {
      customizedHtml = customizedHtml.replace(
        /(<body[^>]*>)/i,
        `$1${agentqHeader}`
      );
    } else {
      customizedHtml = agentqHeader + customizedHtml;
    }

    // Add custom CSS to override any existing ZAP styling
    const agentqStyles = `
      <style>
        /* Modern, professional styling for AgentQ Security Reports */
        * {
          box-sizing: border-box;
        }

        body {
          font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif !important;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
          margin: 0 !important;
          padding: 20px !important;
          line-height: 1.6 !important;
          color: #2d3748 !important;
        }

        /* AGGRESSIVE HIDING - Remove ALL images and ZAP branding */
        img, picture, svg {
          display: none !important;
        }

        /* Hide any element containing unwanted text */
        *:contains("ZAP"), *:contains("Checkmarx"), *:contains("Sites:"), *:contains("AgentQ Security Scanner") {
          display: none !important;
        }

        /* Hide specific ZAP branding patterns */
        [class*="zap"], [id*="zap"], [class*="logo"], [id*="logo"] {
          display: none !important;
        }

        /* Main content container */
        body > * {
          max-width: 1200px;
          margin: 0 auto;
        }

        /* Enhanced headings */
        h1, h2, h3, h4, h5, h6 {
          color: #1a202c !important;
          font-weight: 700 !important;
          margin: 24px 0 16px 0 !important;
          line-height: 1.3 !important;
        }

        h1 {
          font-size: 2.5rem !important;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent !important;
          background-clip: text !important;
        }

        h2 {
          font-size: 1.875rem !important;
          color: #2d3748 !important;
          border-bottom: 3px solid #667eea !important;
          padding-bottom: 8px !important;
        }

        h3 {
          font-size: 1.5rem !important;
          color: #4a5568 !important;
        }

        /* Beautiful vulnerability summary table */
        table {
          border-collapse: collapse !important;
          width: 100% !important;
          margin: 20px 0 !important;
          background: white !important;
          border-radius: 12px !important;
          overflow: hidden !important;
          box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
          border: none !important;
        }

        th, td {
          padding: 16px 20px !important;
          text-align: left !important;
          border: none !important;
          border-bottom: 1px solid #e2e8f0 !important;
        }

        th {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          color: white !important;
          font-weight: 600 !important;
          font-size: 0.95rem !important;
          text-transform: uppercase !important;
          letter-spacing: 0.5px !important;
        }

        tr:nth-child(even) {
          background-color: #f7fafc !important;
        }

        tr:hover {
          background-color: #edf2f7 !important;
          transform: translateY(-1px) !important;
          transition: all 0.2s ease !important;
        }

        /* Enhanced risk level styling */
        td:contains("High"), .risk-high {
          color: #e53e3e !important;
          font-weight: 700 !important;
          background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%) !important;
          border-radius: 6px !important;
          padding: 8px 12px !important;
          text-align: center !important;
          text-transform: uppercase !important;
          font-size: 0.875rem !important;
          letter-spacing: 0.5px !important;
        }

        td:contains("Medium"), .risk-medium {
          color: #dd6b20 !important;
          font-weight: 700 !important;
          background: linear-gradient(135deg, #feebc8 0%, #fbd38d 100%) !important;
          border-radius: 6px !important;
          padding: 8px 12px !important;
          text-align: center !important;
          text-transform: uppercase !important;
          font-size: 0.875rem !important;
          letter-spacing: 0.5px !important;
        }

        td:contains("Low"), .risk-low {
          color: #38a169 !important;
          font-weight: 700 !important;
          background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%) !important;
          border-radius: 6px !important;
          padding: 8px 12px !important;
          text-align: center !important;
          text-transform: uppercase !important;
          font-size: 0.875rem !important;
          letter-spacing: 0.5px !important;
        }

        td:contains("Informational"), .risk-info {
          color: #3182ce !important;
          font-weight: 700 !important;
          background: linear-gradient(135deg, #bee3f8 0%, #90cdf4 100%) !important;
          border-radius: 6px !important;
          padding: 8px 12px !important;
          text-align: center !important;
          text-transform: uppercase !important;
          font-size: 0.875rem !important;
          letter-spacing: 0.5px !important;
        }

        /* Enhanced content sections */
        p {
          margin: 12px 0 !important;
          color: #4a5568 !important;
          font-size: 1rem !important;
        }

        /* Beautiful cards for content sections */
        body > div, body > section {
          background: white !important;
          border-radius: 12px !important;
          padding: 24px !important;
          margin: 20px 0 !important;
          box-shadow: 0 4px 6px rgba(0,0,0,0.05) !important;
          border: 1px solid #e2e8f0 !important;
        }

        /* Vulnerability details styling */
        .vulnerability-item {
          background: white !important;
          border-radius: 8px !important;
          padding: 20px !important;
          margin: 16px 0 !important;
          border-left: 4px solid #667eea !important;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        /* Links styling */
        a {
          color: #667eea !important;
          text-decoration: none !important;
          font-weight: 500 !important;
        }

        a:hover {
          color: #764ba2 !important;
          text-decoration: underline !important;
        }

        /* Code and technical content */
        code, pre {
          background: #f7fafc !important;
          border: 1px solid #e2e8f0 !important;
          border-radius: 6px !important;
          padding: 8px 12px !important;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
          font-size: 0.875rem !important;
        }

        /* Responsive design */
        @media (max-width: 768px) {
          body {
            padding: 10px !important;
          }

          table {
            font-size: 0.875rem !important;
          }

          th, td {
            padding: 12px 8px !important;
          }
        }
      </style>
    `;

    // Add styles to head
    if (customizedHtml.includes('</head>')) {
      customizedHtml = customizedHtml.replace('</head>', `${agentqStyles}</head>`);
    } else if (customizedHtml.includes('<head>')) {
      customizedHtml = customizedHtml.replace('<head>', `<head>${agentqStyles}`);
    } else {
      customizedHtml = agentqStyles + customizedHtml;
    }

    return customizedHtml;
  }

  /**
   * Generate HTML report from ZAP JSON data if HTML is not available
   */
  private generateHtmlFromZapReport(zapReport: any): string {
    const summary = zapReport.summary || {};
    const vulnerabilities = zapReport.vulnerabilities || [];
    const scanContext = zapReport.scanContext || {};
    const jsonData = zapReport.json || {};

    // Try to extract vulnerabilities from different possible structures
    let allVulnerabilities = vulnerabilities;
    if (allVulnerabilities.length === 0 && jsonData.alerts) {
      allVulnerabilities = jsonData.alerts.map((alert: any) => ({
        name: alert.name || 'Unknown Vulnerability',
        risk: alert.risk || 'Unknown',
        confidence: alert.confidence || 'Unknown',
        description: alert.description || '',
        solution: alert.solution || '',
        reference: alert.reference || '',
        instances: alert.instances || []
      }));
    }

    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>AgentQ Security Report</title>
        <style>
          * { box-sizing: border-box; }
          body {
            font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            line-height: 1.6;
            color: #2d3748;
          }

          .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
          }
          .summary-item {
            background: white;
            padding: 24px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s ease;
          }
          .summary-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
          }
          .count {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            display: block;
          }
          .high { color: #e53e3e; }
          .medium { color: #dd6b20; }
          .low { color: #38a169; }
          .info { color: #3182ce; }
          .scan-info {
            background: white;
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
          }
          .scan-info h3 {
            color: #2d3748;
            margin-top: 0;
            font-size: 1.5rem;
            font-weight: 700;
          }
          .vulnerability {
            background: white;
            margin: 20px 0;
            padding: 24px;
            border-radius: 12px;
            border-left: 6px solid #667eea;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
          }
          .vulnerability:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
          }
          .vuln-high { border-left-color: #e53e3e; }
          .vuln-medium { border-left-color: #dd6b20; }
          .vuln-low { border-left-color: #38a169; }
          .vuln-info { border-left-color: #3182ce; }
          .vulnerability h3 {
            color: #2d3748;
            margin-top: 0;
            font-size: 1.25rem;
            font-weight: 600;
          }
          .instance {
            background: #f7fafc;
            padding: 16px;
            margin: 12px 0;
            border-radius: 8px;
            font-size: 0.9em;
            border: 1px solid #e2e8f0;
          }
          .no-data {
            text-align: center;
            padding: 60px 40px;
            color: #718096;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
          }
          .no-data h2 {
            color: #38a169;
            font-size: 2rem;
            margin-bottom: 16px;
          }
          .powered-by {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 15px;
            font-weight: 500;
          }
          h1, h2, h3 {
            color: #2d3748;
            font-weight: 700;
          }
          h1 { font-size: 2.5rem; margin: 0 0 10px 0; }
          h2 { font-size: 1.875rem; margin: 30px 0 20px 0; }
          h3 { font-size: 1.5rem; margin: 20px 0 15px 0; }
          p { margin: 12px 0; color: #4a5568; }
          strong { color: #2d3748; font-weight: 600; }
        </style>
      </head>
      <body>
        <h1>AgentQ Security Report</h1>
        <p style="margin-bottom: 30px; color: #4a5568; font-size: 1.1rem;">Generated on: ${new Date().toLocaleString()}</p>

        <div class="summary">
          <div class="summary-item">
            <div class="count high">${summary.highRisk || 0}</div>
            <div>High Risk</div>
          </div>
          <div class="summary-item">
            <div class="count medium">${summary.mediumRisk || 0}</div>
            <div>Medium Risk</div>
          </div>
          <div class="summary-item">
            <div class="count low">${summary.lowRisk || 0}</div>
            <div>Low Risk</div>
          </div>
          <div class="summary-item">
            <div class="count info">${summary.informational || 0}</div>
            <div>Informational</div>
          </div>
        </div>

        <div class="scan-info">
          <h3>📊 Scan Information</h3>
          <p><strong>Total Issues:</strong> ${summary.totalIssues || allVulnerabilities.length || 0}</p>
          <p><strong>URLs Scanned:</strong> ${summary.urlsScanned || scanContext.urls?.length || 'Unknown'}</p>
        </div>
    `;

    if (allVulnerabilities.length > 0) {
      html += '<h2>🚨 Vulnerabilities Found</h2>';
      allVulnerabilities.forEach((vuln: any, index: number) => {
        const severityClass = vuln.risk?.toLowerCase() || 'info';
        html += `
          <div class="vulnerability vuln-${severityClass}">
            <h3>${index + 1}. ${vuln.name || 'Unknown Vulnerability'}</h3>
            <p><strong>Risk Level:</strong> <span class="${severityClass}">${vuln.risk || 'Unknown'}</span></p>
            <p><strong>Confidence:</strong> ${vuln.confidence || 'Unknown'}</p>
            ${vuln.description ? `<p><strong>Description:</strong> ${vuln.description}</p>` : ''}
            ${vuln.solution ? `<p><strong>Solution:</strong> ${vuln.solution}</p>` : ''}
            ${vuln.reference ? `<p><strong>Reference:</strong> ${vuln.reference}</p>` : ''}
            ${vuln.instances && vuln.instances.length > 0 ? `
              <h4>Affected Instances:</h4>
              ${vuln.instances.map((instance: any) => `
                <div class="instance">
                  <strong>URL:</strong> ${instance.uri || 'Unknown'}<br>
                  <strong>Method:</strong> ${instance.method || 'Unknown'}
                  ${instance.param ? `<br><strong>Parameter:</strong> ${instance.param}` : ''}
                  ${instance.evidence ? `<br><strong>Evidence:</strong> ${instance.evidence}` : ''}
                </div>
              `).join('')}
            ` : ''}
          </div>
        `;
      });
    } else {
      html += `
        <div class="no-data">
          <h2>✅ No Vulnerabilities Found</h2>
          <p>The security scan completed successfully with no vulnerabilities detected.</p>
          ${jsonData.scanStatus === 'no_data' ? '<p><em>Note: No HTTP traffic was captured through the ZAP proxy during test execution.</em></p>' : ''}
        </div>
      `;
    }

    html += '</body></html>';
    return html;
  }

  /**
   * Find a test result by ID, projectId, and testRunId
   */
  async findByIdAndProjectAndTestRun(resultId: string, projectId: string, testRunId: string): Promise<TestResult | undefined> {
    return this.testResultsRepository
      .createQueryBuilder('testResult')
      .leftJoinAndSelect('testResult.testRun', 'testRun')
      .leftJoinAndSelect('testResult.testCase', 'testCase')
      .where('testResult.id = :resultId', { resultId })
      .andWhere('testRun.id = :testRunId', { testRunId })
      .andWhere('testRun.projectId = :projectId', { projectId })
      .getOne();
  }

  /**
   * Find a test result by ID only (for DAST integration)
   */
  async findById(resultId: string): Promise<TestResult | undefined> {
    return this.testResultsRepository.findOne({
      where: { id: resultId },
      relations: ['testRun', 'testCase']
    });
  }

  /**
   * Generate vulnerability description from ZAP report summary and scan context
   */
  private generateVulnerabilityDescription(zapReport: any): string | null {
    try {
      console.log(`🔍 Debug: generateVulnerabilityDescription called with:`, {
        hasReport: !!zapReport,
        hasSummary: !!zapReport?.summary,
        summaryContent: zapReport?.summary
      });

      if (!zapReport || !zapReport.summary) {
        console.warn(`⚠️ Debug: Missing zapReport or summary:`, {
          hasReport: !!zapReport,
          hasSummary: !!zapReport?.summary
        });
        return null;
      }

      const summary = zapReport.summary;
      const scanContext = zapReport.scanContext || {};

      // Build vulnerability description with summary and scan context
      const description = [
        '=== DAST Security Scan Summary ===',
        `Total Issues Found: ${summary.totalIssues || 0}`,
        `• High Risk: ${summary.highRisk || 0}`,
        `• Medium Risk: ${summary.mediumRisk || 0}`,
        `• Low Risk: ${summary.lowRisk || 0}`,
        `• Informational: ${summary.informational || 0}`,
        '',
        `URLs Scanned: ${summary.urlsScanned || 0}`,
        '',
        '=== Scan Context ===',
        `Sites Tested: ${(scanContext.sites || []).length}`,
        ...(scanContext.sites || []).slice(0, 10).map((site: string) => `• ${site}`),
        ...(scanContext.sites && scanContext.sites.length > 10 ? [`... and ${scanContext.sites.length - 10} more sites`] : []),
        '',
        `Hosts Analyzed: ${(scanContext.hosts || []).length}`,
        ...(scanContext.hosts || []).slice(0, 10).map((host: string) => `• ${host}`),
        ...(scanContext.hosts && scanContext.hosts.length > 10 ? [`... and ${scanContext.hosts.length - 10} more hosts`] : []),
        '',
        '=== Risk Assessment ===',
        summary.highRisk > 0 ? '🔴 HIGH RISK vulnerabilities detected - Immediate attention required' : '',
        summary.mediumRisk > 0 ? '🟡 MEDIUM RISK vulnerabilities detected - Should be addressed' : '',
        summary.lowRisk > 0 ? '🟢 LOW RISK vulnerabilities detected - Consider addressing' : '',
        summary.totalIssues === 0 ? '✅ No security vulnerabilities detected' : '',
        '',
        `Scan completed at: ${zapReport.timestamp || new Date().toISOString()}`,
        `Client ID: ${zapReport.clientId || 'N/A'}`,
        '',
        'For detailed vulnerability information, see the full ZAP security report.'
      ].filter(line => line !== '').join('\n');

      return description;
    } catch (error) {
      console.error('Error generating vulnerability description:', error);
      return null;
    }
  }
}
