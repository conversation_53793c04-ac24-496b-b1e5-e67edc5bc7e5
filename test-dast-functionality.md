# DAST Test Result Functionality Test Plan

## Overview
This document outlines how to test the enhanced DAST (Dynamic Application Security Testing) functionality for test results.

## What Was Implemented

### Backend Changes
1. **TestResult Entity Enhanced**:
   - Added DAST-specific status values (New, Open, In Progress, Fixed/Remediated, etc.)
   - Added DAST-specific fields: vulnerabilityDescription, originalSeverity, adjustedSeverity, affectedUrls, requestResponse, remediationGuidance
   - Made testCaseId nullable for DAST results

2. **CreateTestResultDto Enhanced**:
   - Made testCaseId optional
   - Added all DAST-specific fields as optional
   - Only status field is required

3. **Service Methods Updated**:
   - updateTestResult method handles DAST fields
   - bulkUpdateTestResults method handles DAST fields
   - updateTestResultByTestCaseId method handles DAST fields

4. **Controller Enhanced**:
   - Bulk update endpoint accepts DAST fields

5. **Database Migration**:
   - Added DAST status enum values
   - Added DAST-specific columns to test_results table
   - Made testCaseId nullable

### Frontend Changes
1. **TestCaseDastResultHistory.vue**:
   - Only requires status field for saving
   - All other DAST fields are optional
   - Sends all DAST fields to backend

## Testing Steps

### Prerequisites
1. Run the migration to add DAST fields:
   ```bash
   npm run migration:run
   ```

2. Ensure you have:
   - A project with DAST test cases (type = "security - dast")
   - A DAST test run created
   - Test results in the DAST test run

### Test Case 1: Single DAST Test Result Update
1. Navigate to a DAST test run detail page
2. Click on a test case to open the DAST result history modal
3. Try to save with only status selected (should work)
4. Try to save with status + some DAST fields filled (should work)
5. Try to save with no status selected (should not save)
6. Verify that all DAST fields are saved correctly in the database

### Test Case 2: Bulk DAST Test Result Update
1. Navigate to a DAST test run detail page
2. Select multiple test results using checkboxes
3. Click "Bulk Update" button
4. In the bulk update modal, set only the status (should work)
5. Set status + some DAST fields (should work)
6. Verify all selected test results are updated correctly

### Test Case 3: DAST Field Validation
1. Open DAST result history modal
2. Verify that only status field shows as required (*)
3. Verify that all other fields are optional
4. Test saving with various combinations of filled/empty fields

### Test Case 4: Database Verification
1. After updating DAST test results, check the database:
   ```sql
   SELECT id, status, vulnerabilityDescription, originalSeverity, adjustedSeverity, 
          affectedUrls, requestResponse, remediationGuidance 
   FROM test_results 
   WHERE testRunId = 'your-dast-test-run-id' 
   ORDER BY updatedAt DESC;
   ```

2. Verify that:
   - DAST status values are stored correctly
   - DAST-specific fields contain the expected data
   - NULL values are handled properly for empty fields

### Test Case 5: History Tracking
1. Update a DAST test result multiple times with different values
2. Check the test result history to ensure all changes are tracked
3. Verify that DAST-specific fields show in the history

## Expected Results

### Success Criteria
- ✅ Only status field is required for DAST test results
- ✅ All DAST-specific fields can be saved and retrieved
- ✅ Bulk updates work with DAST fields
- ✅ History tracking includes DAST fields
- ✅ Database migration runs successfully
- ✅ No compilation errors in backend or frontend

### API Endpoints Enhanced
- `PUT /projects/{projectId}/test-runs/{testRunId}/test-results/{testResultId}` - Single update with DAST fields
- `PUT /projects/{projectId}/test-runs/{testRunId}/test-results/bulk-update` - Bulk update with DAST fields

### Database Schema Changes
- New enum values in test_results_status_enum
- New columns: vulnerabilityDescription, originalSeverity, adjustedSeverity, affectedUrls, requestResponse, remediationGuidance
- testCaseId column made nullable

## Troubleshooting

### Common Issues
1. **Migration fails**: Check if enum values already exist
2. **Validation errors**: Ensure only status is required in frontend
3. **Database errors**: Verify column types match entity definitions
4. **Frontend errors**: Check that API calls include all DAST fields

### Debug Steps
1. Check browser console for frontend errors
2. Check backend logs for API errors
3. Verify database schema matches entity definitions
4. Test API endpoints directly with tools like Postman
